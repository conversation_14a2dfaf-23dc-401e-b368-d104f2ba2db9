

import React from 'react';
import { Link } from 'react-router-dom';
import { MapPin, Phone, Mail, Facebook, Instagram, Youtube } from 'lucide-react';
import NewsletterSignup from '@/components/features/NewsletterSignup';
import GoogleReviewsWidget from '@/components/widgets/GoogleReviewsWidget';
import TripAdvisorWidget from '@/components/widgets/TripAdvisorWidget';
import { reviewWidgetConfig } from '@/config/reviewWidgets';
import { scrollToTop } from '@/hooks/useScrollToTop';
import { GlassFooter } from '@/components/ui/glass-header';

interface FooterProps {
  isDarkBackground?: boolean;
}

const Footer: React.FC<FooterProps> = ({ isDarkBackground = false }) => {
  const handleLinkClick = () => {
    scrollToTop(); // Scroll to top for all footer navigation links
  };

  // Luxury color scheme classes - consistent with contact page design
  const textClasses = {
    primary: 'text-[#F2EEE6]',
    secondary: 'text-[#F2EEE6]/70',
    hover: 'hover:text-[#D4C2A4]',
    border: 'border-[#D4C2A4]/20',
    bg: 'bg-[#D4C2A4]/5 hover:bg-[#D4C2A4]/10',
    borderHover: 'hover:border-[#D4C2A4]/40'
  };

  return (
    <footer id="footer" role="contentinfo" className="mt-auto">
      <GlassFooter variant="crystal" intensity="light" className="bg-[#16191D] text-[#F2EEE6]">
        {/* Luxury background overlay with deep charcoal */}
        <div className="absolute inset-0 bg-gradient-to-t from-[#16191D]/90 to-[#16191D]/70 pointer-events-none"></div>
        <div className="container mx-auto px-4 py-8 md:py-12 relative z-10">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8 mb-6 md:mb-8">
          {/* Company Info */}
          <div className="sm:col-span-2 lg:col-span-1">
            <div className="flex items-center space-x-2 mb-4">
              <img
                src="/photos/fulllogo.svg"
                alt="Warriors of Africa Safari Logo"
                className="h-25 w-25 sm:h-32 sm:w-32 md:h-35 md:w-35 lg:h-40 lg:w-40 object-contain drop-shadow-xl"
              />
             
            </div>
            <p className={`mb-4 leading-relaxed text-sm md:text-base font-open-sans ${textClasses.secondary}`}>
              Creating unforgettable safari experiences in Tanzania for over 15 years.
              Your gateway to authentic African adventures.
            </p>
            <div className="flex space-x-3 md:space-x-4">
              <a
                href="https://facebook.com/warriorsafricasafari"
                target="_blank"
                rel="noopener noreferrer"
                className={`${textClasses.secondary} ${textClasses.hover} transition-all duration-500 p-2 rounded-lg ${textClasses.bg} backdrop-blur-sm border ${textClasses.border} ${textClasses.borderHover} hover:shadow-sm hover:scale-105`}
                aria-label="Follow us on Facebook"
              >
                <Facebook className="h-4 w-4 md:h-5 md:w-5" />
              </a>
              <a
                href="https://instagram.com/warriors_ofafricasafari"
                target="_blank"
                rel="noopener noreferrer"
                className={`${textClasses.secondary} ${textClasses.hover} transition-all duration-500 p-2 rounded-lg ${textClasses.bg} backdrop-blur-sm border ${textClasses.border} ${textClasses.borderHover} hover:shadow-sm hover:scale-105`}
                aria-label="Follow us on Instagram"
              >
                <Instagram className="h-4 w-4 md:h-5 md:w-5" />
              </a>
              <a
                href="https://youtube.com/@warriorsofafricasafari"
                target="_blank"
                rel="noopener noreferrer"
                className={`${textClasses.secondary} ${textClasses.hover} transition-all duration-500 p-2 rounded-lg ${textClasses.bg} backdrop-blur-sm border ${textClasses.border} ${textClasses.borderHover} hover:shadow-sm hover:scale-105`}
                aria-label="Subscribe to our YouTube channel"
              >
                <Youtube className="h-4 w-4 md:h-5 md:w-5" />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className={`text-base md:text-lg font-cormorant font-semibold mb-3 md:mb-4 ${textClasses.primary}`}>Quick Links</h3>
            <ul className="space-y-1 md:space-y-2 text-sm md:text-base">
              <li><Link to="/tours" onClick={handleLinkClick} className={`font-open-sans ${textClasses.secondary} ${textClasses.hover} transition-all duration-500 hover:underline`}>All Tours</Link></li>
              <li><Link to="/destinations" onClick={handleLinkClick} className={`font-open-sans ${textClasses.secondary} ${textClasses.hover} transition-all duration-500 hover:underline`}>Destinations</Link></li>
              <li><Link to="/about" onClick={handleLinkClick} className={`font-open-sans ${textClasses.secondary} ${textClasses.hover} transition-all duration-500 hover:underline`}>About Us</Link></li>
              <li><Link to="/reviews" onClick={handleLinkClick} className={`font-open-sans ${textClasses.secondary} ${textClasses.hover} transition-all duration-500 hover:underline`}>Reviews</Link></li>
              <li><Link to="/blog" onClick={handleLinkClick} className={`font-open-sans ${textClasses.secondary} ${textClasses.hover} transition-all duration-500 hover:underline`}>Travel Blog</Link></li>
              <li><Link to="/travel-resources" onClick={handleLinkClick} className={`font-open-sans ${textClasses.secondary} ${textClasses.hover} transition-all duration-500 hover:underline`}>Travel Resources</Link></li>
              <li><Link to="/gallery" onClick={handleLinkClick} className={`font-open-sans ${textClasses.secondary} ${textClasses.hover} transition-all duration-500 hover:underline`}>Photo Gallery</Link></li>
            </ul>
          </div>

          {/* Tour Types */}
          <div>
            <h3 className={`text-base md:text-lg font-cormorant font-semibold mb-3 md:mb-4 ${textClasses.primary}`}>Safari Types</h3>
            <ul className="space-y-1 md:space-y-2 text-sm md:text-base">
              <li><Link to="/tours" onClick={handleLinkClick} className={`font-open-sans ${textClasses.secondary} ${textClasses.hover} transition-all duration-500 hover:underline`}>Luxury Safari</Link></li>
              <li><Link to="/tours" onClick={handleLinkClick} className={`font-open-sans ${textClasses.secondary} ${textClasses.hover} transition-all duration-500 hover:underline`}>Budget Safari</Link></li>
              <li><Link to="/tours" onClick={handleLinkClick} className={`font-open-sans ${textClasses.secondary} ${textClasses.hover} transition-all duration-500 hover:underline`}>Family Safari</Link></li>
              <li><Link to="/tours" onClick={handleLinkClick} className={`font-open-sans ${textClasses.secondary} ${textClasses.hover} transition-all duration-500 hover:underline`}>Photography Tours</Link></li>
              <li><Link to="/tours" onClick={handleLinkClick} className={`font-open-sans ${textClasses.secondary} ${textClasses.hover} transition-all duration-500 hover:underline`}>Cultural Tours</Link></li>
              <li><Link to="/tours" onClick={handleLinkClick} className={`font-open-sans ${textClasses.secondary} ${textClasses.hover} transition-all duration-500 hover:underline`}>Kilimanjaro Climbing</Link></li>
            </ul>
          </div>

          {/* Contact & Newsletter */}
          <div>
            <h3 className={`text-base md:text-lg font-cormorant font-semibold mb-3 md:mb-4 ${textClasses.primary}`}>Contact Us</h3>
            <div className="space-y-2 md:space-y-3 mb-4 md:mb-6">
              <div className={`flex items-center text-sm font-open-sans ${textClasses.secondary}`}>
                <MapPin className="h-3 w-3 md:h-4 md:w-4 mr-2 flex-shrink-0" />
                <span>P.O.BOX 1477, Moshono-Arusha</span>
              </div>
              <div className={`flex items-center text-sm font-open-sans ${textClasses.secondary}`}>
                <Phone className="h-3 w-3 md:h-4 md:w-4 mr-2 flex-shrink-0" />
                <span>+25566121379</span>
              </div>
              <div className={`flex items-center text-sm font-open-sans ${textClasses.secondary}`}>
                <Mail className="h-3 w-3 md:h-4 md:w-4 mr-2 flex-shrink-0" />
                <span><EMAIL></span>
              </div>
            </div>

            {/* Newsletter Signup */}
            <NewsletterSignup variant="footer" />
          </div>
        </div>

        {/* Reviews Section */}
        <div className={`border-t pt-6 md:pt-8 mb-6 md:mb-8 ${isDarkBackground ? 'border-white/20' : 'border-black/8 dark:border-white/8'}`}>
          <div className="max-w-3xl mx-auto">
            <h4 className={`text-sm font-medium mb-4 text-center ${textClasses.primary}`}>Trusted by Travelers Worldwide</h4>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <GoogleReviewsWidget
                businessId={reviewWidgetConfig.google.businessId}
                rating={reviewWidgetConfig.google.rating}
                reviewCount={reviewWidgetConfig.google.reviewCount}
                className="flex-1 max-w-sm"
              />
              <TripAdvisorWidget
                profileUrl={reviewWidgetConfig.tripadvisor.profileUrl}
                rating={reviewWidgetConfig.tripadvisor.rating}
                certificateYear={reviewWidgetConfig.tripadvisor.certificateYear}
                reviewCount={reviewWidgetConfig.tripadvisor.reviewCount}
                className="flex-1 max-w-sm"
              />
            </div>
          </div>
        </div>

        {/* Certifications & Partnerships */}
        <div className={`border-t pt-6 md:pt-8 mb-6 md:mb-8 border-[#D4C2A4]/20`}>
          <h4 className={`text-base md:text-lg font-semibold mb-3 md:mb-4 text-center ${textClasses.primary}`}>Our Certifications & Partners</h4>
          <div className={`flex flex-wrap justify-center items-center gap-4 md:gap-8 text-xs md:text-sm ${textClasses.secondary}`}>
            <div className={`px-4 py-2 rounded-full backdrop-blur-sm transition-all duration-500 hover:scale-105 ${textClasses.bg} border ${textClasses.border} ${textClasses.borderHover}`}>Tanzania Tourism Board</div>
            <div className={`px-4 py-2 rounded-full backdrop-blur-sm transition-all duration-500 hover:scale-105 ${textClasses.bg} border ${textClasses.border} ${textClasses.borderHover}`}>TATO Member</div>
            <div className={`px-4 py-2 rounded-full backdrop-blur-sm transition-all duration-500 hover:scale-105 ${textClasses.bg} border ${textClasses.border} ${textClasses.borderHover}`}>ISO Certified</div>
            <div className={`px-4 py-2 rounded-full backdrop-blur-sm transition-all duration-500 hover:scale-105 ${textClasses.bg} border ${textClasses.border} ${textClasses.borderHover}`}>Carbon Neutral</div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className={`border-t pt-6 md:pt-8 flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0 border-[#D4C2A4]/20`}>
          <div className={`text-xs md:text-sm text-center md:text-left ${textClasses.secondary}`}>
            © {new Date().getFullYear()} Warriors of Africa Safari Tours. All rights reserved.
          </div>
          <div className="flex flex-wrap justify-center gap-4 md:gap-6 text-xs md:text-sm">
            <Link to="/privacy" onClick={handleLinkClick} className={`${textClasses.secondary} ${textClasses.hover} transition-all duration-500 hover:underline px-2 py-1 rounded backdrop-blur-sm hover:bg-[#D4C2A4]/10`}>
              Privacy Policy
            </Link>
            <Link to="/terms" onClick={handleLinkClick} className={`${textClasses.secondary} ${textClasses.hover} transition-all duration-500 hover:underline px-2 py-1 rounded backdrop-blur-sm hover:bg-[#D4C2A4]/10`}>
              Terms of Service
            </Link>
            <Link to="/cookies" onClick={handleLinkClick} className={`${textClasses.secondary} ${textClasses.hover} transition-all duration-500 hover:underline px-2 py-1 rounded backdrop-blur-sm hover:bg-[#D4C2A4]/10`}>
              Cookie Policy
            </Link>
          </div>
        </div>
      </div>
    </GlassFooter>
    </footer>
  );
};

export default Footer;
